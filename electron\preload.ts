import { contextBridge, ipcRenderer } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Add any APIs you want to expose to the renderer process here
  platform: process.platform,
  versions: process.versions,

  // Window control APIs
  windowMinimize: () => ipcRenderer.invoke('window-minimize'),
  windowClose: () => ipcRenderer.invoke('window-close'),
  windowToggleAlwaysOnTop: () => ipcRenderer.invoke('window-toggle-always-on-top'),
  windowIsAlwaysOnTop: () => ipcRenderer.invoke('window-is-always-on-top'),
  windowToggleMaximize: () => ipcRenderer.invoke('window-toggle-maximize'),
  windowIsMaximized: () => ipcRenderer.invoke('window-is-maximized'),
  restoreMainWindow: () => ipcRenderer.invoke('restore-main-window'),
  toggleDevTools: () => ipcRenderer.invoke('toggle-dev-tools'),

  // AI Chat APIs
  sendChatMessage: (message: string, history: any[]) => ipcRenderer.invoke('send-chat-message', message, history),
  onChatStreamChunk: (callback: (chunk: string) => void) => {
    ipcRenderer.on('chat-stream-chunk', (event, chunk) => callback(chunk))
  },

  // Storage APIs
  saveData: (key: string, data: any) => ipcRenderer.invoke('save-data', key, data),
  loadData: (key: string) => ipcRenderer.invoke('load-data', key)
})
