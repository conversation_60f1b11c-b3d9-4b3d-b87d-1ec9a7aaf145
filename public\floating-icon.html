<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 60px;
            height: 60px;
            background: transparent;
            overflow: hidden;
            cursor: pointer;
            user-select: none;
        }
        
        .floating-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .floating-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
        }
        
        .icon-text {
            color: white;
            font-size: 20px;
            font-weight: bold;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }
    </style>
</head>
<body>
    <div class="floating-icon pulse" onclick="restoreWindow()">
        <div class="icon-text">AI</div>
    </div>
    
    <script>
        function restoreWindow() {
            // Send message to main process to restore window
            if (window.electronAPI && window.electronAPI.restoreMainWindow) {
                window.electronAPI.restoreMainWindow();
            } else {
                // Fallback: use IPC directly
                const { ipcRenderer } = require('electron');
                ipcRenderer.invoke('restore-main-window');
            }
        }

        // Also handle double-click
        document.addEventListener('dblclick', restoreWindow);
    </script>
</body>
</html>
