@echo off
echo ========================================
echo AI Chat Widget 内网打包工具
echo ========================================
echo.

echo [1/4] 检查依赖...
if not exist "node_modules" (
    echo 错误: 请先运行 npm install 安装依赖
    pause
    exit /b 1
)

echo [2/4] 构建前端应用...
call npm run build:vite
if %errorlevel% neq 0 (
    echo 错误: 前端构建失败
    pause
    exit /b 1
)

echo [3/4] 构建主进程...
call npm run build:electron
if %errorlevel% neq 0 (
    echo 错误: 主进程构建失败
    pause
    exit /b 1
)

echo [4/4] 打包exe文件...
call npx electron-builder
if %errorlevel% neq 0 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo 输出目录: dist-build\
echo 安装版: AI Chat Widget Setup.exe
echo 便携版: AI-Chat-Widget-Portable.exe
echo ========================================
pause
