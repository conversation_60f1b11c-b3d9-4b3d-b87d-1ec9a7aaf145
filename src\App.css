/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: transparent;
  user-select: none;
}

/* Main widget container */
.ai-chat-widget {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* Maximized state adjustments */
.ai-chat-widget.maximized {
  border-radius: 0;
  box-shadow: none;
  border: none;
}

/* Smooth resize transitions */
.ai-chat-widget * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Window controls bar */
.window-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-app-region: drag; /* Make the title bar draggable */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.window-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  flex: 1;
  text-align: center;
  pointer-events: none;
}

.control-buttons {
  display: flex;
  gap: 4px;
  -webkit-app-region: no-drag; /* Buttons should not drag the window */
  flex-wrap: wrap;
}

.control-btn {
  width: 22px;
  height: 22px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.pin-btn.active, .debug-btn.active, .maximize-btn.active {
  background: rgba(255, 215, 0, 0.3);
  color: #ffd700;
}

.history-btn:hover {
  background: rgba(0, 122, 255, 0.3);
  color: #007aff;
}

.config-btn:hover {
  background: rgba(52, 199, 89, 0.3);
  color: #34c759;
}

.debug-btn:hover {
  background: rgba(255, 69, 58, 0.3);
  color: #ff453a;
}

.clear-btn:hover {
  background: rgba(255, 152, 0, 0.3);
  color: #ff9800;
}

.maximize-btn:hover {
  background: rgba(90, 200, 250, 0.3);
  color: #5ac8fa;
}

.close-btn:hover {
  background: rgba(255, 59, 48, 0.8);
}

/* Chat container */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Messages area */
.messages-area {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.welcome-message {
  text-align: center;
  color: #666;
  margin-top: 50%;
  transform: translateY(-50%);
}

.welcome-message p {
  margin: 8px 0;
  font-size: 16px;
}

.message {
  display: flex;
  margin-bottom: 12px;
  animation: messageSlideIn 0.3s ease-out;
}

.user-message {
  justify-content: flex-end;
}

.ai-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 80%;
  padding: 10px 14px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  animation: messageContentFadeIn 0.4s ease-out;
}

.user-message .message-content {
  background: #007AFF;
  color: white;
  border-bottom-right-radius: 6px;
}

.ai-message .message-content {
  background: #f0f0f0;
  color: #333;
  border-bottom-left-radius: 6px;
}

/* Input area */
.input-area {
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 14px;
  resize: none;
  outline: none;
  font-family: inherit;
  background: white;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  border-color: #007AFF;
}

.send-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.send-button:hover:not(:disabled) {
  background: #0056CC;
  transform: scale(1.02);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.message-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* Maximized window input adjustments */
.ai-chat-widget.maximized .input-area {
  padding: 16px 24px;
  max-width: 800px;
  margin: 0 auto;
}

.ai-chat-widget.maximized .message-input {
  max-width: none;
  min-height: 40px;
}

.ai-chat-widget.maximized .messages-area {
  max-width: 800px;
  margin: 0 auto;
  padding: 16px 24px;
}

.ai-chat-widget.maximized .message-content {
  max-width: 70%;
}

/* Loading indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  background: #f0f0f0;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
  max-width: 80%;
  margin-bottom: 12px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #999;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Panel styles */
.history-panel, .config-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.98);
  animation: slideIn 0.3s ease-out;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.02);
}

.panel-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.close-panel-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.close-panel-btn:hover {
  background: rgba(255, 59, 48, 0.8);
  color: white;
}

/* History panel */
.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.no-history {
  text-align: center;
  color: #999;
  margin-top: 50px;
  font-style: italic;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  padding: 12px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.history-time {
  font-size: 11px;
  color: #999;
  margin-bottom: 6px;
}

.history-message {
  font-size: 14px;
  line-height: 1.4;
}

.history-message.user {
  color: #007AFF;
}

.history-message.ai {
  color: #333;
}

/* Config panel */
.config-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.config-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.config-section:last-child {
  border-bottom: none;
}

.config-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.config-item label {
  color: #666;
  font-weight: 500;
}

.config-item span {
  color: #333;
}

.api-status {
  color: #ff6b6b !important;
  font-weight: 500;
}

.config-action-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.config-action-btn:hover {
  background: #0056CC;
  transform: translateY(-1px);
}

.config-section p {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 4px 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Scrollbar styling */
.messages-area::-webkit-scrollbar {
  width: 6px;
}

.messages-area::-webkit-scrollbar-track {
  background: transparent;
}

.messages-area::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages-area::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes messageContentFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive design for smaller windows */
@media (max-width: 350px) {
  .window-title {
    font-size: 12px;
  }

  .control-btn {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .message-content {
    max-width: 90%;
    font-size: 13px;
  }

  .input-area {
    padding: 8px 12px;
  }
}

@media (max-height: 400px) {
  .messages-area {
    padding: 12px;
  }

  .welcome-message {
    margin-top: 30%;
  }

  .welcome-message p {
    font-size: 14px;
  }
}
