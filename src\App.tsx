import { useState, useEffect, useRef } from 'react'
import './App.css'

// Declare the electronAPI interface
declare global {
  interface Window {
    electronAPI: {
      platform: string
      versions: any
      windowMinimize: () => Promise<void>
      windowClose: () => Promise<void>
      windowToggleAlwaysOnTop: () => Promise<boolean>
      windowIsAlwaysOnTop: () => Promise<boolean>
      windowToggleMaximize: () => Promise<boolean>
      windowIsMaximized: () => Promise<boolean>
      restoreMainWindow: () => Promise<void>
      toggleDevTools: () => Promise<boolean>
      sendChatMessage: (message: string, history: any[]) => Promise<{success: boolean, response: string, error?: string}>
      onChatStreamChunk: (callback: (chunk: string) => void) => void
      saveData: (key: string, data: any) => Promise<boolean>
      loadData: (key: string) => Promise<any>
    }
  }
}

function App() {
  const [messages, setMessages] = useState<Array<{id: number, text: string, isUser: boolean, timestamp: number}>>([])
  const [inputText, setInputText] = useState('')
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [streamingMessageId, setStreamingMessageId] = useState<number | null>(null)
  const [showHistory, setShowHistory] = useState(false)
  const [showConfig, setShowConfig] = useState(false)
  const [devToolsOpen, setDevToolsOpen] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Check initial window status and load chat history
    if (window.electronAPI) {
      window.electronAPI.windowIsAlwaysOnTop().then(setIsAlwaysOnTop)
      window.electronAPI.windowIsMaximized().then(setIsMaximized)
      loadChatHistory()

      // Setup streaming response listener
      window.electronAPI.onChatStreamChunk((chunk: string) => {
        if (streamingMessageId) {
          setMessages(prev => prev.map(msg =>
            msg.id === streamingMessageId
              ? { ...msg, text: msg.text + chunk }
              : msg
          ))
        }
      })

      // Check window state periodically to handle external maximize/restore
      const checkWindowState = () => {
        window.electronAPI.windowIsMaximized().then(setIsMaximized)
        window.electronAPI.windowIsAlwaysOnTop().then(setIsAlwaysOnTop)
      }

      const interval = setInterval(checkWindowState, 1000)
      return () => clearInterval(interval)
    }
  }, [streamingMessageId])

  useEffect(() => {
    // Auto scroll to bottom when new messages are added
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  useEffect(() => {
    // Save chat history when messages change
    if (messages.length > 0) {
      saveChatHistory()
    }
  }, [messages])

  const loadChatHistory = async () => {
    if (window.electronAPI) {
      const history = await window.electronAPI.loadData('chatHistory')
      if (history && Array.isArray(history)) {
        setMessages(history)
      }
    }
  }

  const saveChatHistory = async () => {
    if (window.electronAPI) {
      await window.electronAPI.saveData('chatHistory', messages)
    }
  }

  const handleMinimize = async () => {
    if (window.electronAPI) {
      await window.electronAPI.windowMinimize()
    }
  }

  const handleClose = async () => {
    if (window.electronAPI) {
      await window.electronAPI.windowClose()
    }
  }

  const handleToggleAlwaysOnTop = async () => {
    if (window.electronAPI) {
      const newState = await window.electronAPI.windowToggleAlwaysOnTop()
      setIsAlwaysOnTop(newState)
    }
  }

  const handleToggleMaximize = async () => {
    if (window.electronAPI) {
      const newState = await window.electronAPI.windowToggleMaximize()
      setIsMaximized(newState)
    }
  }

  const handleToggleDevTools = async () => {
    if (window.electronAPI) {
      const newState = await window.electronAPI.toggleDevTools()
      setDevToolsOpen(newState)
    }
  }

  const handleShowHistory = () => {
    setShowHistory(true)
  }

  const handleShowConfig = () => {
    setShowConfig(true)
  }

  const handleSendMessage = async () => {
    if (inputText.trim() && !isLoading) {
      const userMessage = {
        id: Date.now(),
        text: inputText,
        isUser: true,
        timestamp: Date.now()
      }

      setMessages(prev => [...prev, userMessage])
      setInputText('')
      setIsLoading(true)

      // Create AI response message
      const aiMessageId = Date.now() + 1
      const aiMessage = {
        id: aiMessageId,
        text: '',
        isUser: false,
        timestamp: Date.now()
      }

      setMessages(prev => [...prev, aiMessage])
      setStreamingMessageId(aiMessageId)

      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.sendChatMessage(inputText, messages)

          if (!result.success) {
            // Update message with error response
            setMessages(prev => prev.map(msg =>
              msg.id === aiMessageId
                ? { ...msg, text: result.response }
                : msg
            ))
          }
        }
      } catch (error) {
        console.error('Error sending message:', error)
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessageId
            ? { ...msg, text: '抱歉，发送消息时出现错误。请稍后重试。' }
            : msg
        ))
      } finally {
        setIsLoading(false)
        setStreamingMessageId(null)
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const clearHistory = async () => {
    setMessages([])
    if (window.electronAPI) {
      await window.electronAPI.saveData('chatHistory', [])
    }
  }

  return (
    <div className={`ai-chat-widget ${isMaximized ? 'maximized' : ''}`}>
      {/* Window Controls */}
      <div className="window-controls">
        <div className="window-title">AI助手</div>
        <div className="control-buttons">
          <button
            className="control-btn history-btn"
            onClick={handleShowHistory}
            title="查看历史记录"
          >
            📋
          </button>
          <button
            className="control-btn config-btn"
            onClick={handleShowConfig}
            title="配置设置"
          >
            ⚙️
          </button>
          <button
            className={`control-btn debug-btn ${devToolsOpen ? 'active' : ''}`}
            onClick={handleToggleDevTools}
            title={devToolsOpen ? '关闭调试' : '开启调试'}
          >
            🐛
          </button>
          <button
            className="control-btn clear-btn"
            onClick={clearHistory}
            title="清除历史"
          >
            🗑️
          </button>
          <button
            className={`control-btn pin-btn ${isAlwaysOnTop ? 'active' : ''}`}
            onClick={handleToggleAlwaysOnTop}
            title={isAlwaysOnTop ? '取消置顶' : '置顶窗口'}
          >
            📌
          </button>
          <button
            className={`control-btn maximize-btn ${isMaximized ? 'active' : ''}`}
            onClick={handleToggleMaximize}
            title={isMaximized ? '还原窗口' : '最大化'}
          >
            {isMaximized ? '🗗' : '🗖'}
          </button>
          <button
            className="control-btn minimize-btn"
            onClick={handleMinimize}
            title="最小化到桌面"
          >
            ➖
          </button>
          <button
            className="control-btn close-btn"
            onClick={handleClose}
            title="关闭到托盘"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Chat Area */}
      <div className="chat-container">
        {showHistory ? (
          <div className="history-panel">
            <div className="panel-header">
              <h3>聊天历史</h3>
              <button
                className="close-panel-btn"
                onClick={() => setShowHistory(false)}
              >
                ✕
              </button>
            </div>
            <div className="history-content">
              {messages.length === 0 ? (
                <p className="no-history">暂无聊天记录</p>
              ) : (
                <div className="history-list">
                  {messages.map(message => (
                    <div key={message.id} className="history-item">
                      <div className="history-time">
                        {new Date(message.timestamp).toLocaleString()}
                      </div>
                      <div className={`history-message ${message.isUser ? 'user' : 'ai'}`}>
                        <strong>{message.isUser ? '你' : 'AI'}:</strong> {message.text}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : showConfig ? (
          <div className="config-panel">
            <div className="panel-header">
              <h3>配置设置</h3>
              <button
                className="close-panel-btn"
                onClick={() => setShowConfig(false)}
              >
                ✕
              </button>
            </div>
            <div className="config-content">
              <div className="config-section">
                <h4>AI 配置</h4>
                <div className="config-item">
                  <label>API 密钥状态:</label>
                  <span className="api-status">
                    {/* 这里可以添加API密钥检查逻辑 */}
                    需要配置 DeepSeek API 密钥
                  </span>
                </div>
                <div className="config-item">
                  <label>模型:</label>
                  <span>DeepSeek Chat</span>
                </div>
              </div>

              <div className="config-section">
                <h4>窗口设置</h4>
                <div className="config-item">
                  <label>当前状态:</label>
                  <span>
                    {isAlwaysOnTop ? '已置顶' : '未置顶'} |
                    {isMaximized ? '已最大化' : '正常大小'}
                  </span>
                </div>
              </div>

              <div className="config-section">
                <h4>数据管理</h4>
                <div className="config-item">
                  <label>聊天记录:</label>
                  <span>{messages.length} 条消息</span>
                </div>
                <button
                  className="config-action-btn"
                  onClick={clearHistory}
                >
                  清除所有记录
                </button>
              </div>

              <div className="config-section">
                <h4>帮助信息</h4>
                <p>查看 API_SETUP.md 了解如何配置 DeepSeek API</p>
                <p>查看 FEATURES.md 了解所有功能特性</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="messages-area">
            {messages.length === 0 ? (
              <div className="welcome-message">
                <p>👋 你好！我是你的AI助手</p>
                <p>有什么可以帮助你的吗？</p>
              </div>
            ) : (
              messages.map(message => (
                <div
                  key={message.id}
                  className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}
                >
                  <div className="message-content">
                    {message.text}
                  </div>
                </div>
              ))
            )}
            {isLoading && (
              <div className="ai-message">
                <div className="typing-indicator">
                  <span>AI正在思考</span>
                  <div className="typing-dots">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}

        {/* Input Area */}
        <div className="input-area">
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="输入你的问题..."
            className="message-input"
            rows={2}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            className="send-button"
            disabled={!inputText.trim() || isLoading}
          >
            {isLoading ? '发送中...' : '发送'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default App
