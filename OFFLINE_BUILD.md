# 内网环境打包指南

## 概述
本项目已配置为支持内网环境下的打包，使用国内镜像源来下载Electron二进制文件。

## 内网打包配置

### 1. 环境变量设置（可选）
如果需要使用特定的镜像源，可以设置以下环境变量：

```bash
# 使用淘宝镜像
set ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
set ELECTRON_CACHE_DIR=./electron-cache

# 或者使用其他镜像源
set ELECTRON_MIRROR=https://registry.npmmirror.com/-/binary/electron/
```

### 2. 打包命令

#### 构建应用
```bash
npm run build
```

#### 生成exe文件
```bash
npm run build:exe
```

这将生成两种格式的exe文件：
- **安装版**: `dist-build/AI Chat Widget Setup.exe` - 需要安装的版本
- **便携版**: `dist-build/AI-Chat-Widget-Portable.exe` - 免安装直接运行

### 3. 内网环境特殊配置

项目已在 `package.json` 中配置了以下内网友好设置：

- **镜像源**: 使用 `https://npmmirror.com/mirrors/electron/` 作为Electron下载镜像
- **缓存目录**: 本地缓存到 `./electron-cache` 目录
- **跳过重建**: `npmRebuild: false` 避免原生模块重建问题

### 4. 离线打包步骤

如果完全离线环境，可以按以下步骤操作：

1. **预下载依赖** (在有网络的环境中)：
   ```bash
   npm install
   npm run build:electron
   ```

2. **预下载Electron二进制文件**：
   ```bash
   npx electron-builder install-app-deps
   ```

3. **打包应用**：
   ```bash
   npm run build:exe
   ```

### 5. 故障排除

#### 如果遇到网络问题：
- 检查是否设置了正确的代理
- 尝试使用不同的镜像源
- 确保防火墙允许npm和electron-builder访问网络

#### 如果遇到权限问题：
- 以管理员身份运行命令提示符
- 检查杀毒软件是否阻止了文件操作

#### 如果遇到缓存问题：
```bash
# 清除npm缓存
npm cache clean --force

# 清除electron缓存
rmdir /s electron-cache
```

### 6. 输出文件说明

打包完成后，在 `dist-build` 目录中会生成：

- `AI Chat Widget Setup.exe` - 安装程序，会在用户系统中安装应用
- `AI-Chat-Widget-Portable.exe` - 便携版，可直接运行，无需安装
- `win-unpacked/` - 未打包的应用文件夹

推荐使用便携版进行测试和分发。
