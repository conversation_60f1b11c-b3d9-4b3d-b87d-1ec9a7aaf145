import { app, BrowserWindow, ipc<PERSON>ain, Tray, Menu, nativeImage } from 'electron'
import * as path from 'path'

const isDev = !app.isPackaged
let mainWindow: BrowserWindow | null = null
let tray: Tray | null = null
let floatingIcon: BrowserWindow | null = null

function createTrayIcon(): void {
  // Create a simple tray icon using system default
  const trayIcon = nativeImage.createEmpty()

  // Try to use the app icon or create a simple one
  try {
    const iconPath = path.join(__dirname, '../public/vite.svg')
    if (require('fs').existsSync(iconPath)) {
      const icon = nativeImage.createFromPath(iconPath).resize({ width: 16, height: 16 })
      tray = new Tray(icon)
    } else {
      // Use system default
      tray = new Tray(trayIcon)
    }
  } catch (error) {
    tray = new Tray(trayIcon)
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: '显示窗口',
      click: () => {
        showMainWindow()
      }
    },
    {
      label: '退出',
      click: () => {
        app.quit()
      }
    }
  ])

  tray.setToolTip('AI Chat Widget')
  tray.setContextMenu(contextMenu)

  // Double click to show window
  tray.on('double-click', () => {
    showMainWindow()
  })
}

function createFloatingIcon(): void {
  floatingIcon = new BrowserWindow({
    width: 60,
    height: 60,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    transparent: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  })

  // Load floating icon HTML
  if (isDev) {
    floatingIcon.loadURL('http://localhost:5174/floating-icon.html')
  } else {
    floatingIcon.loadFile(path.join(__dirname, '../dist/floating-icon.html'))
  }

  // Handle click on floating icon through webContents
  floatingIcon.webContents.on('before-input-event', (event, input) => {
    if (input.type === 'mouseDown') {
      showMainWindow()
    }
  })

  floatingIcon.hide()
}

function showMainWindow(): void {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore()
    }
    mainWindow.show()
    mainWindow.focus()
    if (floatingIcon) {
      floatingIcon.hide()
    }
  }
}

function hideMainWindow(): void {
  if (mainWindow) {
    mainWindow.hide()
    if (floatingIcon) {
      floatingIcon.show()
      // Position floating icon at bottom right
      const { screen } = require('electron')
      const primaryDisplay = screen.getPrimaryDisplay()
      const { width, height } = primaryDisplay.workAreaSize
      floatingIcon.setPosition(width - 80, height - 80)
    }
  }
}

function createWindow(): void {
  console.log('Creating window...')

  // Create the browser window as a desktop widget
  mainWindow = new BrowserWindow({
    height: 600,
    width: 450,
    minHeight: 400,
    minWidth: 350,
    maxHeight: isDev ? undefined : 800, // Allow maximize in dev mode
    maxWidth: isDev ? undefined : 600,
    frame: false, // Remove default window frame
    skipTaskbar: true, // Don't show in taskbar
    alwaysOnTop: false, // Default not on top, user can toggle
    resizable: true, // Allow resizing
    show: false, // Don't show until ready
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: isDev ? false : true // Allow dev tools in development
    }
  })

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show')
    if (mainWindow) {
      mainWindow.show()
    }
  })

  // Handle window close - minimize to tray instead
  mainWindow.on('close', (event) => {
    event.preventDefault()
    hideMainWindow()
  })

  // Load the app
  if (isDev) {
    console.log('Loading development URL: http://localhost:5174')
    mainWindow.loadURL('http://localhost:5174')
    // Enable DevTools for debugging
    mainWindow.webContents.openDevTools()
  } else {
    console.log('Loading production file')
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Handle load errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription)
  })

  // Create tray and floating icon
  createTrayIcon()
  createFloatingIcon()
}

// Handle window controls from renderer
ipcMain.handle('window-minimize', () => {
  if (mainWindow) {
    hideMainWindow()
  }
})

ipcMain.handle('window-close', () => {
  if (mainWindow) {
    hideMainWindow()
  }
})

ipcMain.handle('window-toggle-always-on-top', () => {
  if (mainWindow) {
    const isAlwaysOnTop = mainWindow.isAlwaysOnTop()
    mainWindow.setAlwaysOnTop(!isAlwaysOnTop)
    return !isAlwaysOnTop
  }
  return false
})

ipcMain.handle('window-is-always-on-top', () => {
  if (mainWindow) {
    return mainWindow.isAlwaysOnTop()
  }
  return false
})

// Handle maximize/restore
ipcMain.handle('window-toggle-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.restore()
      return false
    } else {
      mainWindow.maximize()
      return true
    }
  }
  return false
})

ipcMain.handle('window-is-maximized', () => {
  if (mainWindow) {
    return mainWindow.isMaximized()
  }
  return false
})

// Handle dev tools toggle
let devToolsOpen = false
ipcMain.handle('toggle-dev-tools', () => {
  if (mainWindow) {
    if (devToolsOpen) {
      mainWindow.webContents.closeDevTools()
      devToolsOpen = false
    } else {
      mainWindow.webContents.openDevTools()
      devToolsOpen = true
    }
    return devToolsOpen
  }
  return false
})

// Handle restore main window
ipcMain.handle('restore-main-window', () => {
  showMainWindow()
})

// Handle data storage
const fs = require('fs')
const os = require('os')
const dataPath = path.join(os.homedir(), '.ai-chat-widget')

// Ensure data directory exists
if (!fs.existsSync(dataPath)) {
  fs.mkdirSync(dataPath, { recursive: true })
}

ipcMain.handle('save-data', async (event, key: string, data: any) => {
  try {
    const filePath = path.join(dataPath, `${key}.json`)
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2))
    return true
  } catch (error) {
    console.error('Error saving data:', error)
    return false
  }
})

ipcMain.handle('load-data', async (event, key: string) => {
  try {
    const filePath = path.join(dataPath, `${key}.json`)
    if (fs.existsSync(filePath)) {
      const data = fs.readFileSync(filePath, 'utf8')
      return JSON.parse(data)
    }
    return null
  } catch (error) {
    console.error('Error loading data:', error)
    return null
  }
})

// Handle AI chat requests with streaming
ipcMain.handle('send-chat-message', async (event, message: string, history: any[]) => {
  try {
    // 注意：这里需要替换为实际的DeepSeek API密钥
    const API_KEY = process.env.DEEPSEEK_API_KEY || 'YOUR_DEEPSEEK_API_KEY'

    const messages = [
      ...history.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      })),
      {
        role: 'user',
        content: message
      }
    ]

    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: messages,
        stream: true,
        max_tokens: 1000,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // Handle streaming response
    const reader = response.body?.getReader()
    let fullResponse = ''

    if (reader) {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n').filter(line => line.trim())

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              const content = parsed.choices?.[0]?.delta?.content
              if (content) {
                fullResponse += content
                // Send partial response to renderer
                mainWindow?.webContents.send('chat-stream-chunk', content)
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    }

    return { success: true, response: fullResponse }
  } catch (error) {
    console.error('Error calling DeepSeek API:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      response: '抱歉，AI服务暂时不可用。请检查网络连接或API配置。'
    }
  }
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
