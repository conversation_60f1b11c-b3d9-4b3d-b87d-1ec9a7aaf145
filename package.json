{"name": "ai-chat-widget", "version": "1.0.0", "description": "AI Chat Desktop Widget - A floating AI assistant for your desktop", "main": "dist-electron/main.js", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5174 --timeout 30000 && npx electron .", "electron": "npx electron .", "build": "npm run build:vite && npm run build:electron", "build:vite": "vite build", "build:electron": "tsc -p electron", "preview": "vite preview"}, "keywords": ["electron", "vite", "react", "desktop", "ai", "chat", "widget", "assistant"], "author": "", "license": "MIT", "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^29.1.4", "typescript": "^5.2.2", "vite": "^5.2.0", "wait-on": "^7.2.0"}, "dependencies": {"axios": "^1.11.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}